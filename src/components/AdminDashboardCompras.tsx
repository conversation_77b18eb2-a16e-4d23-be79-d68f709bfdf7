import React, { useState, useEffect, useRef } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler
} from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Line, Doughnut } from 'react-chartjs-2';
import { Settings } from 'lucide-react';
import KpiComprasInlineForm from './KpiComprasInlineForm';
import KpiComprasHistorySection from './KpiComprasHistorySection';
import { getKpisCompras, createKpiCompras, update<PERSON><PERSON><PERSON>ompras, deleteKpiCompras, type KpiComprasData } from '@/app/actions/kpis-compras';
import { getTacometroMaxProveedores, setTacometroMaxProveedores } from '@/app/actions/app-config';
import { formatWeekDatesForChart } from '@/lib/utils/weekUtils';
import jsPDF from 'jspdf';
import {
  Users,
  TrendingUp,
  DollarSign,
  Activity,
  Droplet,
  Package,
  Info
} from "lucide-react";

// Registrar componentes de Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  ChartTooltip,
  Legend,
  Filler,
  ChartDataLabels
);

// Componente SemicircleGauge
interface SemicircleGaugeProps {
  actualValue: number
  maxValue?: number
  label?: string
}

function SemicircleGauge({ actualValue, maxValue = 40, label = "Neutral" }: SemicircleGaugeProps) {
  // Calcular el valor inicial para evitar animación desde 0 en la primera carga
  const getInitialValue = () => {
    const score = Math.min(8, Math.max(1, Math.ceil((actualValue / maxValue) * 8)));
    return ((score - 1) / 7) * 100;
  };

  const [animatedValue, setAnimatedValue] = useState(getInitialValue);
  const animationRef = useRef<number | null>(null);
  const isFirstRender = useRef(true);

  // Calcular el score basado en el valor actual y máximo
  const getScore = (value: number, max: number) => {
    const step = max / 8;
    if (value >= max) return 8;
    if (value >= max - step) return 7;
    if (value >= max - (step * 2)) return 6;
    if (value >= max - (step * 3)) return 5;
    if (value >= max - (step * 4)) return 4;
    if (value >= max - (step * 5)) return 3;
    if (value >= max - (step * 6)) return 2;
    return 1;
  };

  const score = getScore(actualValue, maxValue);

  // Convertir score (1-8) a porcentaje (0-100) para el tacómetro
  const targetValue = ((score - 1) / 7) * 100;

  // Animar el valor cuando cambie
  useEffect(() => {
    // En la primera renderización, establecer el valor sin animación
    if (isFirstRender.current) {
      setAnimatedValue(targetValue);
      isFirstRender.current = false;
      return;
    }

    // Cancelar animación anterior si existe
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    const duration = 1200; // 1.2 segundos de animación
    const startTime = Date.now();
    const startValue = animatedValue;
    const valueChange = targetValue - startValue;

    // Si no hay cambio significativo, no animar
    if (Math.abs(valueChange) < 0.5) {
      setAnimatedValue(targetValue);
      return;
    }

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Función de easing suave (ease-out-cubic)
      const easeOut = 1 - Math.pow(1 - progress, 3);

      const currentValue = startValue + (valueChange * easeOut);
      setAnimatedValue(currentValue);

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        // Asegurar que termine exactamente en el valor objetivo
        setAnimatedValue(targetValue);
        animationRef.current = null;
      }
    };

    animationRef.current = requestAnimationFrame(animate);

    // Cleanup function
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = null;
      }
    };
  }, [targetValue]);

  // Calcular la posición del indicador basado en el valor animado
  // Coordenadas exactas basadas en las líneas SVG del semicírculo
  const centerX = 72  // Centro horizontal del semicírculo
  const centerY = 68  // Centro vertical del semicírculo (línea base)
  const radius = 59   // Radio del semicírculo (igual al de las líneas SVG)

  // Calcular ángulo: de π (180°) a 0 (0°) para ir de izquierda a derecha
  // animatedValue va de 0 a 100, lo convertimos a ángulo de π a 0
  // Verificación de puntos clave:
  // - animatedValue = 0 → angle = π → x = 72 + 59*(-1) = 13, y = 68 - 59*0 = 68 ✓
  // - animatedValue = 100 → angle = 0 → x = 72 + 59*1 = 131, y = 68 - 59*0 = 68 ✓
  // - animatedValue = 50 → angle = π/2 → x = 72 + 59*0 = 72, y = 68 - 59*1 = 9 ✓
  const angle = Math.PI - (animatedValue / 100) * Math.PI

  // Posición del indicador exactamente sobre la línea del semicírculo
  const indicatorX = centerX + radius * Math.cos(angle)
  const indicatorY = centerY - radius * Math.sin(angle)

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        height: "100%",
      }}
    >
      <div
        style={{
          position: "relative",
          width: "144px",
          height: "78px",
          display: "flex",
          justifyContent: "center",
          alignItems: "flex-end",
        }}
      >
        <svg width="144" height="78" viewBox="0 0 144 78">
          {/* Segmento rojo */}
          <path
            d="M 13 67.99999999999999 A 59 59 0 0 1 20.699799159192082 38.85742987153037"
            stroke="#EA3943"
            strokeWidth="6"
            strokeLinecap="round"
            fill="none"
          />

          {/* Segmento naranja */}
          <path
            d="M 25.25491104204376 32.001435329825206 A 59 59 0 0 1 49.136580399325936 13.610074056278464"
            stroke="#EA8C00"
            strokeWidth="6"
            strokeLinecap="round"
            fill="none"
          />

          {/* Segmento amarillo */}
          <path
            d="M 56.928700281788366 10.957420072336895 A 59 59 0 0 1 87.07129971821165 10.957420072336895"
            stroke="#F3D42F"
            strokeWidth="6"
            strokeLinecap="round"
            fill="none"
          />

          {/* Segmento verde claro */}
          <path
            d="M 94.86341960067408 13.61007405627847 A 59 59 0 0 1 118.74508895795626 32.00143532982522"
            stroke="#93D900"
            strokeWidth="6"
            strokeLinecap="round"
            fill="none"
          />

          {/* Segmento verde */}
          <path
            d="M 123.30020084080792 38.85742987153038 A 59 59 0 0 1 131 68"
            stroke="#16C784"
            strokeWidth="6"
            strokeLinecap="round"
            fill="none"
          />

          {/* Indicador circular con animación JavaScript */}
          <circle
            cx={indicatorX}
            cy={indicatorY}
            r="7"
            fill="none"
            stroke="white"
            strokeWidth="4"
          />
          <circle
            cx={indicatorX}
            cy={indicatorY}
            r="5"
            fill="black"
          />
        </svg>

        {/* Texto del valor y etiqueta */}
        <div
          style={{
            position: "absolute",
            bottom: "0px",
            width: "100%",
            textAlign: "center",
            marginBottom: "2.5px",
          }}
        >
          <div>
            <div>
              <span
                style={{
                  fontSize: "32px",
                  fontWeight: "bold",
                  color: "#000",
                  fontFamily: "system-ui, -apple-system, sans-serif",
                }}
              >
                {actualValue}
              </span>
            </div>
            <span
              className="text-xs text-gray-500"
              style={{
                display: "block",
                fontFamily: "system-ui, -apple-system, sans-serif",
              }}
            >
              {label}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

interface AdminDashboardComprasProps {
  user: {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role?: string;
  };
  openChartDialog: (title: string, chartData: any, chartOptions: any, chartType: 'line' | 'pie' | 'doughnut') => void;
  getSmartDataLabelsConfig: (formatter: (value: any) => string) => any;
  getChartLayout: () => any;
  getDoughnutDataLabelsConfig?: () => any;
  getDoughnutChartOptions?: () => any;
  kpisCompras?: any[];
  loadingKpisCompras?: boolean;
}

const AdminDashboardCompras = ({
  user,
  openChartDialog,
  getSmartDataLabelsConfig,
  getChartLayout,
  getDoughnutDataLabelsConfig,
  getDoughnutChartOptions,
  kpisCompras: kpisComprasFromParent = [],
  loadingKpisCompras: loadingKpisComprasFromParent = false
}: AdminDashboardComprasProps) => {
  // Estados específicos para el panel de Compras
  const [timeRangeCompras, setTimeRangeCompras] = useState("4w");
  const [showInlineFormCompras, setShowInlineFormCompras] = useState(false);
  const [showKpiHistoryCompras, setShowKpiHistoryCompras] = useState(false);
  const [editingKpiCompras, setEditingKpiCompras] = useState<KpiComprasData | null>(null);
  const [isAddingOldWeekCompras, setIsAddingOldWeekCompras] = useState(false);
  const [showAnalyticsCompras, setShowAnalyticsCompras] = useState(false);
  const [showFiltersCompras, setShowFiltersCompras] = useState(false);
  const [selectedKpisCompras, setSelectedKpisCompras] = useState<string[]>([]);

  // Usar los datos del componente padre si están disponibles, sino usar estados locales
  const [kpisCompras, setKpisCompras] = useState<any[]>(kpisComprasFromParent);
  const [loadingKpisCompras, setLoadingKpisCompras] = useState(loadingKpisComprasFromParent);
  const [loadingPdfDownload, setLoadingPdfDownload] = useState(false);

  // Estados para configuración del tacómetro
  const [maxValueProveedores, setMaxValueProveedores] = useState(40);
  const [showConfigDropdown, setShowConfigDropdown] = useState(false);
  const [loadingConfig, setLoadingConfig] = useState(false);

  // Datos mock para el panel de Compras
  const mockDataCompras = {
    indicadoresCompras: {
      numeroProveedoresActivos: 24,
      porcentajeReporteGanancia: 92.5,
      preciosPromedioCompra: 21.85,
      diferencialPrecioPemex: -1.25,
      porcentajeCompraPorProveedor: [
        { proveedor: "Proveedor A", porcentaje: 35.2, color: "#3b82f6" },
        { proveedor: "Proveedor B", porcentaje: 28.7, color: "#10b981" },
        { proveedor: "Proveedor C", porcentaje: 18.5, color: "#f59e0b" },
        { proveedor: "Proveedor D", porcentaje: 12.3, color: "#8b5cf6" },
        { proveedor: "Otros", porcentaje: 5.3, color: "#ef4444" }
      ]
    },
    evolucionIndicadoresCompras: [
      { mes: "Ene", proveedores: 22, reporteGanancia: 89.2, precioPromedio: 21.45, diferencial: -1.15, porcentajeA: 32.1, porcentajeB: 30.2, porcentajeC: 20.1, porcentajeD: 12.6, porcentajeOtros: 5.0 },
      { mes: "Feb", proveedores: 23, reporteGanancia: 91.1, precioPromedio: 21.62, diferencial: -1.18, porcentajeA: 33.5, porcentajeB: 29.8, porcentajeC: 19.2, porcentajeD: 12.1, porcentajeOtros: 5.4 },
      { mes: "Mar", proveedores: 21, reporteGanancia: 88.7, precioPromedio: 21.38, diferencial: -1.32, porcentajeA: 31.8, porcentajeB: 31.1, porcentajeC: 19.8, porcentajeD: 12.8, porcentajeOtros: 4.5 },
      { mes: "Abr", proveedores: 24, reporteGanancia: 93.2, precioPromedio: 21.78, diferencial: -1.22, porcentajeA: 34.2, porcentajeB: 28.5, porcentajeC: 18.9, porcentajeD: 12.7, porcentajeOtros: 5.7 },
      { mes: "May", proveedores: 23, reporteGanancia: 90.8, precioPromedio: 21.71, diferencial: -1.28, porcentajeA: 33.8, porcentajeB: 29.1, porcentajeC: 18.2, porcentajeD: 13.1, porcentajeOtros: 5.8 },
      { mes: "Jun", proveedores: 24, reporteGanancia: 92.5, precioPromedio: 21.85, diferencial: -1.25, porcentajeA: 35.2, porcentajeB: 28.7, porcentajeC: 18.5, porcentajeD: 12.3, porcentajeOtros: 5.3 },
    ]
  };

  // Estados para datos de compras - inicializar con datos mock como en ventas
  const [indicadoresComprasActuales, setIndicadoresComprasActuales] = useState(mockDataCompras.indicadoresCompras);
  const [evolucionComprasActual, setEvolucionComprasActual] = useState<any[]>(mockDataCompras.evolucionIndicadoresCompras);

  // Funciones para manejar KPIs de Compras
  const handleNewKpiCompras = () => {
    setEditingKpiCompras(null);
    setIsAddingOldWeekCompras(false);
    setShowInlineFormCompras(true);
  };

  const handleAddOldWeekCompras = () => {
    setEditingKpiCompras(null);
    setIsAddingOldWeekCompras(true);
    setShowInlineFormCompras(true);
  };

  // Función para obtener todas las gráficas de compras disponibles
  const getAllChartsDataCompras = () => {
    const charts = [];

    // Usar datos reales si están disponibles, sino usar datos mock
    let evolutionDataCompras;
    if (kpisCompras.length > 0) {
      // Procesar datos reales de compras
      const filteredKpisCompras = kpisCompras.slice(-4); // Últimas 4 semanas
      evolutionDataCompras = filteredKpisCompras.reverse().map((kpi: any) => ({
        mes: formatWeekDatesForChart(kpi.year, kpi.weekNumber),
        proveedores: kpi.numeroProveedoresActivos,
        reporteGanancia: kpi.porcentajeReporteGanancia,
        precioPromedio: kpi.preciosPromedioCompra,
        diferencial: kpi.diferencialPrecioPemex,
        distribucionProveedores: kpi.distribucionProveedores
      }));
    } else {
      // Usar datos mock si no hay datos reales
      evolutionDataCompras = evolucionComprasActual;
    }

      // 1. Número de Proveedores Activos
      charts.push({
        title: 'Número de Proveedores Activos',
        type: 'line' as const,
        data: {
          labels: evolutionDataCompras.map(item => item.mes),
          datasets: [{
            label: 'Número de Proveedores Activos',
            data: evolutionDataCompras.map(item => Number(Number(item.proveedores).toFixed(0))),
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#3b82f6',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${Number(context.parsed.y).toFixed(0)} proveedores activos`
              }
            },
            datalabels: getSmartDataLabelsConfig((value: any) => Number(value).toFixed(0))
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Número de Proveedores' },
              ticks: { callback: (value: any) => Number(value).toFixed(0) }
            }
          }
        }
      });

      // 2. Porcentaje de Reporte de Ganancia
      charts.push({
        title: 'Porcentaje de Reporte de Ganancia',
        type: 'line' as const,
        data: {
          labels: evolutionDataCompras.map(item => item.mes),
          datasets: [{
            label: 'Porcentaje de Reporte de Ganancia (%)',
            data: evolutionDataCompras.map(item => Number(Number(item.reporteGanancia).toFixed(2))),
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#10b981',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${Number(context.parsed.y).toFixed(2)}%`
              }
            },
            datalabels: getSmartDataLabelsConfig((value: any) => `${Number(value).toFixed(2)}%`)
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Porcentaje (%)' },
              ticks: { callback: (value: any) => `${Number(value).toFixed(2)}%` }
            }
          }
        }
      });

      // 3. Precios Promedios de Compra
      charts.push({
        title: 'Precios Promedios de Compra',
        type: 'line' as const,
        data: {
          labels: evolutionDataCompras.map(item => item.mes),
          datasets: [{
            label: 'Precio Promedio de Compra ($)',
            data: evolutionDataCompras.map(item => Number(Number(item.precioPromedio).toFixed(2))),
            borderColor: '#f59e0b',
            backgroundColor: 'rgba(245, 158, 11, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#f59e0b',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `$${Number(context.parsed.y).toFixed(2)}`
              }
            },
            datalabels: getSmartDataLabelsConfig((value: any) => `$${Number(value).toFixed(2)}`)
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Precio ($)' },
              ticks: { callback: (value: any) => `$${Number(value).toFixed(2)}` }
            }
          }
        }
      });

      // 4. Diferencial de Precio PEMEX
      charts.push({
        title: 'Diferencial entre Precio de Compra Real y Precio de Terminal PEMEX',
        type: 'line' as const,
        data: {
          labels: evolutionDataCompras.map(item => item.mes),
          datasets: [{
            label: 'Diferencial de Precio (%)',
            data: evolutionDataCompras.map(item => Number(Number(item.diferencial).toFixed(2))),
            borderColor: '#8b5cf6',
            backgroundColor: 'rgba(139, 92, 246, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#8b5cf6',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${Number(context.parsed.y).toFixed(2)}%`
              }
            },
            datalabels: getSmartDataLabelsConfig((value: any) => `${Number(value).toFixed(2)}%`)
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Diferencial (%)' },
              ticks: { callback: (value: any) => `${Number(value).toFixed(2)}%` }
            }
          }
        }
      });

      // 5. Evolución de % Compra por Proveedor (Líneas múltiples)
      const proveedoresData = [
        { nombre: 'Proveedor A', color: '#10b981' },
        { nombre: 'Proveedor B', color: '#f59e0b' },
        { nombre: 'Proveedor C', color: '#ef4444' },
        { nombre: 'Proveedor D', color: '#3b82f6' },
        { nombre: 'Otros', color: '#8b5cf6' }
      ];

      const datasets = proveedoresData.map((proveedor) => {
        const data = evolucionComprasActual.map((item: any) => {
          switch(proveedor.nombre) {
            case 'Proveedor A': return Number((item.porcentajeA || 0).toFixed(2));
            case 'Proveedor B': return Number((item.porcentajeB || 0).toFixed(2));
            case 'Proveedor C': return Number((item.porcentajeC || 0).toFixed(2));
            case 'Proveedor D': return Number((item.porcentajeD || 0).toFixed(2));
            case 'Otros': return Number((item.porcentajeOtros || 0).toFixed(2));
            default: return 0;
          }
        });

        return {
          label: proveedor.nombre,
          data: data,
          borderColor: proveedor.color,
          backgroundColor: `${proveedor.color}20`,
          borderWidth: 3,
          fill: false,
          tension: 0.4,
          pointRadius: 4,
          pointHoverRadius: 6,
          pointBackgroundColor: proveedor.color,
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2
        };
      });

      charts.push({
        title: 'Evolución de % Compra por Proveedor',
        type: 'line' as const,
        data: {
          labels: evolucionComprasActual.map((item: any) => item.mes),
          datasets: datasets
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: true, position: 'bottom' as const },
            tooltip: {
              callbacks: {
                label: (context: any) => `${context.dataset.label}: ${Number(context.parsed.y).toFixed(2)}%`
              }
            },
            datalabels: getSmartDataLabelsConfig((value: any) => `${Number(value).toFixed(2)}%`)
          },
          scales: {
            x: { display: true, title: { display: true, text: 'Período' } },
            y: {
              display: true,
              title: { display: true, text: 'Porcentaje (%)' },
              beginAtZero: true,
              max: 100,
              ticks: { callback: (value: any) => `${Number(value).toFixed(2)}%` }
            }
          }
        }
      });

    return charts;
  };

  // Función para descargar todas las gráficas como PDF
  const downloadAllChartsAsPDF = async () => {
    setLoadingPdfDownload(true);
    try {
      const charts = getAllChartsDataCompras();
      if (charts.length === 0) {
        console.warn('No hay gráficos disponibles para exportar');
        setLoadingPdfDownload(false);
        return;
      }

      console.log(`Iniciando exportación PDF de ${charts.length} gráficos de compras...`);

      // Configuración para PDF A4
      const PDF_CONFIG = {
        format: 'a4' as const,
        orientation: 'portrait' as const,
        unit: 'mm' as const,
        chartWidth: 160,  // Ancho en mm para A4
        chartHeight: 90,  // Alto en mm
        margin: 20,
        chartsPerPage: 2
      };

      // Crear documento PDF
      const pdf = new jsPDF({
        orientation: PDF_CONFIG.orientation,
        unit: PDF_CONFIG.unit,
        format: PDF_CONFIG.format
      });

      // Configuración de dimensiones para canvas
      const canvasWidth = 800;  // Píxeles para alta calidad
      const canvasHeight = 450;

      // Agregar header al PDF
      const currentDate = new Date().toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      pdf.setFontSize(20);
      pdf.setTextColor(249, 115, 22); // Color naranja
      pdf.text('Reporte de Gráficas - Compras', 105, 30, { align: 'center' });

      pdf.setFontSize(12);
      pdf.setTextColor(102, 102, 102); // Color gris
      pdf.text(`Cassiopeia Petrolíferos - ${currentDate}`, 105, 40, { align: 'center' });

      let currentY = 60; // Posición Y inicial
      let pageNumber = 1;

      // Procesar cada gráfico
      for (let i = 0; i < charts.length; i++) {
        const chart = charts[i];
        console.log(`Procesando gráfico ${i + 1}/${charts.length}: ${chart.title}`);

        try {
          // Crear canvas temporal para el gráfico
          const chartCanvas = document.createElement('canvas');
          chartCanvas.width = canvasWidth;
          chartCanvas.height = canvasHeight;

          const chartCtx = chartCanvas.getContext('2d');
          if (!chartCtx) {
            console.warn(`No se pudo crear contexto para gráfico: ${chart.title}`);
            continue;
          }

          // Configurar fondo blanco
          chartCtx.fillStyle = '#ffffff';
          chartCtx.fillRect(0, 0, chartCanvas.width, chartCanvas.height);

          // Crear instancia de Chart.js
          const chartInstance = new ChartJS(chartCtx, {
            type: chart.type,
            data: chart.data,
            options: {
              ...chart.options,
              responsive: false,
              maintainAspectRatio: false,
              animation: false,
              devicePixelRatio: 2,
              plugins: {
                ...chart.options.plugins,
                datalabels: getSmartDataLabelsConfig((value: any) => value.toString()) // Configuración para PDF
              }
            }
          });

          // Esperar a que se renderice
          await new Promise(resolve => setTimeout(resolve, 100));

          // Obtener imagen del canvas
          const chartImageData = chartCanvas.toDataURL('image/png', 1.0);

          // Destruir la instancia del gráfico
          chartInstance.destroy();

          // Calcular posición en el PDF
          if (i > 0 && i % PDF_CONFIG.chartsPerPage === 0) {
            pdf.addPage();
            currentY = 60;
            pageNumber++;
          }

          const yPosition = currentY + (i % PDF_CONFIG.chartsPerPage) * (PDF_CONFIG.chartHeight + 20);

          // Agregar título del gráfico
          pdf.setFontSize(14);
          pdf.setTextColor(0, 0, 0);
          pdf.text(chart.title, PDF_CONFIG.margin, yPosition - 5);

          // Agregar imagen del gráfico
          pdf.addImage(
            chartImageData,
            'PNG',
            PDF_CONFIG.margin,
            yPosition,
            PDF_CONFIG.chartWidth,
            PDF_CONFIG.chartHeight
          );

        } catch (error) {
          console.error(`Error al procesar gráfico ${chart.title}:`, error);
        }
      }

      // Agregar número de página
      const totalPages = Math.ceil(charts.length / PDF_CONFIG.chartsPerPage);
      for (let p = 1; p <= totalPages; p++) {
        pdf.setPage(p);
        pdf.setFontSize(10);
        pdf.setTextColor(128, 128, 128);
        pdf.text(`Página ${p} de ${totalPages}`, 105, 287, { align: 'center' });
      }

      // Descargar el PDF
      const fileName = `graficas_compras_${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(fileName);

      console.log('PDF generado exitosamente');
    } catch (error) {
      console.error('Error al generar PDF:', error);
    } finally {
      setLoadingPdfDownload(false);
    }
  };

  const handleSaveKpiCompras = async (data: KpiComprasData) => {
    try {
      let result;
      if (editingKpiCompras) {
        result = await updateKpiCompras(editingKpiCompras.id!, data);
      } else {
        result = await createKpiCompras(data);
      }

      if (result.success) {
        await loadKpisCompras(); // Recargar datos
        setShowInlineFormCompras(false);
        setEditingKpiCompras(null); // Limpiar estado de edición
      } else {
        alert(result.error || 'Error al guardar los datos de compras');
      }
    } catch (error) {
      console.error("Error al guardar KPI de compras:", error);
      alert('Error al guardar los datos de compras');
    }
  };

  const loadKpisCompras = async () => {
    try {
      setLoadingKpisCompras(true);
      const result = await getKpisCompras({ limit: 50 }); // Cargar más datos para el historial
      if (result.success && result.data) {
        setKpisCompras(result.data);
      }
    } catch (error) {
      console.error("Error al cargar KPIs de compras:", error);
    } finally {
      setLoadingKpisCompras(false);
    }
  };

  // Funciones para manejar edición y eliminación de KPIs de compras
  const handleEditKpiCompras = (kpi: KpiComprasData) => {
    setEditingKpiCompras(kpi);
    setIsAddingOldWeekCompras(false);
    setShowInlineFormCompras(true);
  };

  const handleDeleteKpiCompras = async (kpiId: string) => {
    try {
      const result = await deleteKpiCompras(kpiId);
      if (result.success) {
        await loadKpisCompras(); // Recargar datos
      } else {
        alert(result.error || 'Error al eliminar el KPI de compras');
      }
    } catch (error) {
      console.error("Error al eliminar KPI de compras:", error);
      alert('Error al eliminar el KPI de compras');
    }
  };

  // Componente para el icono de información con tooltip
  const CalculationButton = ({ kpiId, calculation }: { kpiId: string; calculation: string }) => {
    const [isVisible, setIsVisible] = useState(false);

    const handleMouseEnter = () => {
      setIsVisible(true);
    };

    const handleMouseLeave = () => {
      setIsVisible(false);
    };

    return (
      <div className="relative inline-block">
        <div
          className="ml-2 p-1 cursor-default"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <Info className="h-3 w-3 text-gray-400" />
        </div>
        {isVisible && (
          <div className="absolute z-50 left-0 top-6 w-60 p-3 bg-white border border-gray-200 rounded-lg shadow-lg">
            <div className="font-medium text-gray-600 text-xs mb-1 flex items-center">
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-1.5"></div>
              Información
            </div>
            <div className="text-xs text-gray-500 leading-relaxed">
              {calculation}
            </div>
            <div className="absolute -top-1 left-2 w-2 h-2 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
          </div>
        )}
      </div>
    );
  };



  // Efecto para sincronizar datos del padre
  useEffect(() => {
    if (kpisComprasFromParent.length > 0) {
      setKpisCompras(kpisComprasFromParent);
    }
  }, [kpisComprasFromParent]);

  useEffect(() => {
    setLoadingKpisCompras(loadingKpisComprasFromParent);
  }, [loadingKpisComprasFromParent]);

  // Efecto para cargar datos iniciales (solo si no vienen del padre)
  useEffect(() => {
    if (kpisComprasFromParent.length === 0) {
      loadKpisCompras();
    }
  }, [kpisComprasFromParent.length]);

  // Efecto para filtrar datos de compras según el rango de tiempo seleccionado
  useEffect(() => {
    if (kpisCompras.length > 0) {
      let weeksToShow = 4;

      // Determinar cuántas semanas mostrar según el filtro
      if (timeRangeCompras === "4w") {
        weeksToShow = 4;
      } else if (timeRangeCompras === "12w") {
        weeksToShow = 12;
      } else if (timeRangeCompras === "24w") {
        weeksToShow = 24;
      }

      // Filtrar KPIs de compras por el período seleccionado
      const filteredKpisCompras = kpisCompras.slice(0, weeksToShow);

      if (filteredKpisCompras.length > 0) {
        // Calcular distribución promedio de proveedores
        const proveedoresMap = new Map<string, { total: number, count: number, color: string }>();
        const colors = ["#3b82f6", "#10b981", "#f59e0b", "#8b5cf6", "#ef4444", "#06b6d4", "#8b5cf6", "#f97316"];

        filteredKpisCompras.forEach(kpi => {
          kpi.distribucionProveedores.forEach((proveedor: any, index: number) => {
            if (!proveedoresMap.has(proveedor.nombre)) {
              proveedoresMap.set(proveedor.nombre, {
                total: 0,
                count: 0,
                color: colors[index % colors.length]
              });
            }
            const existing = proveedoresMap.get(proveedor.nombre)!;
            existing.total += proveedor.porcentaje;
            existing.count += 1;
          });
        });

        const porcentajeCompraPorProveedor = Array.from(proveedoresMap.entries()).map(([nombre, data]) => ({
          proveedor: nombre,
          porcentaje: Number((data.total / data.count).toFixed(2)),
          color: data.color
        }));

        // Actualizar indicadores de compras - todos los indicadores principales muestran última semana
        const latestWeekCompras = filteredKpisCompras[0]; // Primera posición = semana más reciente
        const avgIndicadoresCompras = {
          numeroProveedoresActivos: latestWeekCompras.numeroProveedoresActivos, // Última semana para tacómetro
          porcentajeReporteGanancia: Number(latestWeekCompras.porcentajeReporteGanancia.toFixed(2)), // Última semana para porcentaje de ganancia
          preciosPromedioCompra: Number(latestWeekCompras.preciosPromedioCompra.toFixed(2)), // Última semana para precios promedio
          diferencialPrecioPemex: Number(latestWeekCompras.diferencialPrecioPemex.toFixed(2)), // Última semana para diferencial Pemex
          porcentajeCompraPorProveedor
        };
        setIndicadoresComprasActuales(avgIndicadoresCompras);

        // Actualizar evolución de compras con los datos filtrados
        const evolutionDataCompras = filteredKpisCompras.reverse().map((kpi: any) => ({
          mes: formatWeekDatesForChart(kpi.year, kpi.weekNumber),
          proveedores: kpi.numeroProveedoresActivos,
          reporteGanancia: kpi.porcentajeReporteGanancia,
          precioPromedio: kpi.preciosPromedioCompra,
          diferencial: kpi.diferencialPrecioPemex,
          distribucionProveedores: kpi.distribucionProveedores
        }));
        setEvolucionComprasActual(evolutionDataCompras);
      }
    } else if (kpisCompras.length === 0 && !loadingKpisCompras) {
      // Si no hay datos reales después de cargar, mantener los datos mock
      setIndicadoresComprasActuales(mockDataCompras.indicadoresCompras);
      setEvolucionComprasActual(mockDataCompras.evolucionIndicadoresCompras);
    }
  }, [timeRangeCompras, kpisCompras, loadingKpisCompras]);

  // Efecto para cargar la configuración inicial del tacómetro (global para todos los usuarios)
  useEffect(() => {
    const loadTacometroConfig = async () => {
      try {
        setLoadingConfig(true);
        const maxValue = await getTacometroMaxProveedores();
        setMaxValueProveedores(maxValue);
      } catch (error) {
        console.error('Error al cargar configuración del tacómetro:', error);
        // Mantener valor por defecto
      } finally {
        setLoadingConfig(false);
      }
    };

    loadTacometroConfig();
  }, []);

  // Efecto para cerrar el dropdown de configuración al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showConfigDropdown && !target.closest('.config-dropdown-container')) {
        setShowConfigDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showConfigDropdown]);

  return (
    <div className="space-y-10">
      {/* Mostrar formulario inline si showInlineFormCompras es true */}
      {showInlineFormCompras ? (
        <KpiComprasInlineForm
          onClose={() => setShowInlineFormCompras(false)}
          onSave={handleSaveKpiCompras}
          editingKpi={editingKpiCompras}
          isAddingOldWeek={isAddingOldWeekCompras}
          existingKpis={kpisCompras}
        />
      ) : showKpiHistoryCompras ? (
        <div className="space-y-6">
          {/* Header del historial */}
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <div className="mb-3 sm:mb-0">
              <h1 className="text-2xl font-medium text-gray-900">
                Historial de Semanas - Compras
              </h1>
              <div className="flex items-center mt-1">
                <div className="flex items-center space-x-3">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    {kpisCompras.length} registros
                  </span>
                </div>
                <p className="text-sm text-gray-500 ml-3">Tabla de rendimiento de compras y métricas semanales</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 lg:gap-3">
              <div className="flex items-center bg-white rounded-lg shadow-sm border border-gray-200 p-1">
                <button
                  onClick={() => {
                    // Exportar datos de compras
                    const headers = [
                      'Semana', 'Año', 'Fecha Inicio', 'Fecha Fin', 'Proveedores Activos',
                      '% Reporte Ganancia', 'Precio Promedio Compra', '% Diferencial PEMEX',
                      'Distribución Proveedores', 'Usuario', 'Fecha Creación'
                    ];

                    const csvContent = "data:text/csv;charset=utf-8," +
                      headers.join(",") + "\n" +
                      kpisCompras.map(kpi => [
                        kpi.weekNumber,
                        kpi.year,
                        new Date(kpi.weekStartDate).toLocaleDateString('es-ES'),
                        new Date(kpi.weekEndDate).toLocaleDateString('es-ES'),
                        kpi.numeroProveedoresActivos,
                        kpi.porcentajeReporteGanancia,
                        kpi.preciosPromedioCompra,
                        kpi.diferencialPrecioPemex,
                        `"${kpi.distribucionProveedores.map((p: any) => `${p.nombre}: ${p.porcentaje}%`).join('; ')}"`,
                        `"${(kpi as any).user?.name || (kpi as any).user?.email || 'Usuario desconocido'}"`,
                        (kpi as any).createdAt ? new Date((kpi as any).createdAt).toLocaleDateString('es-ES') : ''
                      ].join(",")).join("\n");

                    const encodedUri = encodeURI(csvContent);
                    const link = document.createElement("a");
                    link.setAttribute("href", encodedUri);
                    link.setAttribute("download", `kpis_compras_${new Date().toISOString().split('T')[0]}.csv`);
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }}
                  className="p-2 text-gray-500 hover:text-primary hover:bg-primary/5 rounded-md transition-all duration-200"
                  title="Exportar todos los datos"
                >
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </button>
                {/* Solo mostrar botones de Analytics y Filtros si hay KPIs registrados */}
                {kpisCompras.length > 0 && (
                  <>
                    <div className="w-px h-6 bg-gray-200 mx-1"></div>
                    <button
                      onClick={() => setShowAnalyticsCompras(!showAnalyticsCompras)}
                      className={`p-2 rounded-md transition-all duration-200 ${
                        showAnalyticsCompras
                          ? 'text-primary bg-primary/10 shadow-sm'
                          : 'text-gray-500 hover:text-primary hover:bg-primary/5'
                      }`}
                      title="Mostrar/Ocultar Analytics"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-eye h-4 w-4">
                        <path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                      </svg>
                    </button>
                    <div className="w-px h-6 bg-gray-200 mx-1"></div>
                    <button
                      onClick={() => setShowFiltersCompras(!showFiltersCompras)}
                      className="p-2 rounded-md transition-all duration-200 text-gray-500 hover:text-primary hover:bg-primary/5"
                      title="Filtros"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-filter h-4 w-4">
                        <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                      </svg>
                    </button>
                  </>
                )}
                <div className="w-px h-6 bg-gray-200 mx-1"></div>
                <button
                  onClick={handleAddOldWeekCompras}
                  className="p-2 text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-all duration-200"
                  title="Agregar Semana Antigua"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-plus h-4 w-4">
                    <path d="M5 12h14"></path>
                    <path d="M12 5v14"></path>
                  </svg>
                </button>
              </div>
              <div className="text-end">
                <button
                  onClick={() => setShowKpiHistoryCompras(false)}
                  className="inline-flex gap-1 items-center font-semibold text-primary text-sm hover:text-primary/80 transition-colors"
                >
                  Regresar
                  <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg">
                    <path d="M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Contenido del historial */}
          <KpiComprasHistorySection
            kpisCompras={kpisCompras}
            loadingKpis={loadingKpisCompras}
            user={user}
            onEditKpi={handleEditKpiCompras}
            onDeleteKpi={handleDeleteKpiCompras}
            onRefresh={loadKpisCompras}
            showAnalytics={showAnalyticsCompras}
            showFilters={showFiltersCompras}
            selectedKpis={selectedKpisCompras}
            onSelectionChange={setSelectedKpisCompras}
          />
        </div>
      ) : (
        <>
          {/* Fila con título a la izquierda y selectores a la derecha */}
          <div className="flex flex-col sm:flex-row justify-between items-center mb-4">
            <div className="mb-3 sm:mb-0">
              <h1 className="text-2xl font-medium text-gray-900">
                Panel de KPIs de Compras
              </h1>
              <div className="flex items-center mt-1">
                <span className={`text-xs md:text-xs lg:text-sm rounded-full px-3 py-1 ${
                  kpisCompras.length > 0
                    ? "bg-green-500/10 text-green-600"
                    : "bg-yellow-500/10 text-yellow-600"
                }`}>
                  {kpisCompras.length > 0 ? "Datos Reales Capturados" : "Datos de Ejemplo"}
                </span>
                {kpisCompras.length > 0 && (
                  <span className="ml-2 text-sm text-gray-500">
                    Última actualización: Semana {kpisCompras[0].weekNumber}/{kpisCompras[0].year}
                  </span>
                )}
              </div>
            </div>

            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 lg:gap-3">
              <div className="flex items-center gap-2">
                <div className="inline-flex rounded-md shadow-sm" role="group">
                  <button
                    type="button"
                    className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                      timeRangeCompras === "4w"
                        ? "bg-primary text-white border-primary"
                        : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                    } rounded-l-lg focus:z-10 focus:ring-2 focus:ring-primary`}
                    onClick={() => setTimeRangeCompras("4w")}
                  >
                    1m
                  </button>
                  <button
                    type="button"
                    className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                      timeRangeCompras === "12w"
                        ? "bg-primary text-white border-primary"
                        : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                    } focus:z-10 focus:ring-2 focus:ring-primary`}
                    onClick={() => setTimeRangeCompras("12w")}
                  >
                    3m
                  </button>
                  <button
                    type="button"
                    className={`px-1.5 sm:px-2 lg:px-3 py-1 sm:py-1.5 text-xs lg:text-sm font-medium border ${
                      timeRangeCompras === "24w"
                        ? "bg-primary text-white border-primary"
                        : "bg-white border-gray-200 hover:bg-gray-100 text-gray-700"
                    } rounded-r-lg focus:z-10 focus:ring-2 focus:ring-primary`}
                    onClick={() => setTimeRangeCompras("24w")}
                  >
                    6m
                  </button>
                </div>
                <button
                  type="button"
                  onClick={downloadAllChartsAsPDF}
                  disabled={loadingPdfDownload}
                  className={`p-2 text-primary border border-transparent rounded-md focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors flex items-center justify-center ${
                    loadingPdfDownload
                      ? 'bg-primary/5 cursor-not-allowed'
                      : 'bg-primary/10 hover:bg-primary/20'
                  }`}
                  title={loadingPdfDownload ? "Generando PDF..." : "Descargar todas las gráficas como PDF"}
                >
                  {loadingPdfDownload ? (
                    <svg className="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                  )}
                </button>
              </div>

              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1.5 lg:gap-2">
                <button
                  type="button"
                  onClick={handleNewKpiCompras}
                  className="p-2 text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                  title="Agregar Datos"
                >
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>

                <div className="text-end">
                  <button
                    type="button"
                    onClick={() => setShowKpiHistoryCompras(!showKpiHistoryCompras)}
                    className="inline-flex gap-1 items-center font-semibold text-primary text-sm hover:text-primary/80 transition-colors"
                  >
                    Ver más
                    <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 448 512" height="16" width="16" xmlns="http://www.w3.org/2000/svg">
                      <path d="M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z"></path>
                    </svg>
                  </button>
                </div>

                {/* Botón de configuración del tacómetro - Solo para administradores */}
                {(user.role === "ADMIN" || user.role === "SUPER_ADMIN") && (
                  <div className="relative config-dropdown-container">
                    <button
                      type="button"
                      onClick={() => setShowConfigDropdown(!showConfigDropdown)}
                      className="p-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors flex items-center justify-center"
                      title="Configurar Tacómetro"
                    >
                      <Settings className="h-4 w-4" />
                    </button>

                    {/* Dropdown de configuración */}
                    {showConfigDropdown && (
                      <div className="absolute right-0 top-full mt-2 w-72 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                        <div className="p-4">
                          <h3 className="text-sm font-medium text-gray-900 mb-2">Configurar Tacómetro</h3>
                          <div className="space-y-3">
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Valor Máximo para "Excepcional"
                              </label>
                              <div className="flex items-center space-x-2">
                                <input
                                  type="text"
                                  inputMode="numeric"
                                  pattern="[0-9]*"
                                  value={maxValueProveedores === 0 ? '' : maxValueProveedores}
                                  onChange={async (e) => {
                                    // Solo permitir números
                                    const value = e.target.value.replace(/[^0-9]/g, '');
                                    if (value === '') {
                                      // Permitir campo vacío temporalmente
                                      setMaxValueProveedores(0);
                                    } else {
                                      const numValue = Number(value);
                                      if (numValue >= 1 && numValue <= 1000) {
                                        setMaxValueProveedores(numValue);
                                        // Guardar en la base de datos
                                        try {
                                          await setTacometroMaxProveedores(numValue);
                                        } catch (error) {
                                          console.error('Error al guardar configuración:', error);
                                        }
                                      }
                                    }
                                  }}
                                  onBlur={async () => {
                                    // Si está vacío al perder el foco, establecer valor por defecto
                                    if (maxValueProveedores === 0) {
                                      setMaxValueProveedores(1);
                                      // Guardar valor por defecto en la base de datos
                                      try {
                                        await setTacometroMaxProveedores(1);
                                      } catch (error) {
                                        console.error('Error al guardar configuración por defecto:', error);
                                      }
                                    }
                                  }}
                                  onKeyDown={(e) => {
                                    // Prevenir caracteres no numéricos
                                    if (!/[0-9]/.test(e.key) && e.key !== 'Backspace' && e.key !== 'Delete' && e.key !== 'Tab' && e.key !== 'Enter' && e.key !== 'ArrowLeft' && e.key !== 'ArrowRight') {
                                      e.preventDefault();
                                    }
                                  }}
                                  className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                  placeholder="Ej: 40"
                                  style={{
                                    MozAppearance: 'textfield'
                                  }}
                                />
                                <span className="text-xs text-gray-500 whitespace-nowrap">proveedores</span>
                              </div>
                              <p className="text-xs text-gray-400 mt-1">Rango: 1-1000 proveedores</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

      {/* Primera Fila - 3 Columnas Compactas */}
      <div className="grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-6 my-2">
        {/* 1. Número de Proveedores Activos */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
          <div className="p-6">
            <div className="flex items-center mb-3">
              <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-blue-500/10 flex items-center justify-center mr-3">
                <Users className="h-5 w-5 text-blue-500" />
              </div>
              <div className="grow">
                <div className="flex items-center">
                  <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">NÚMERO DE PROVEEDORES ACTIVOS</h3>
                  <CalculationButton
                    kpiId="proveedores"
                    calculation="Proveedores con al menos una transacción en el mes"
                  />
                </div>
                <div className="text-xl font-bold text-gray-900">
                  {indicadoresComprasActuales.numeroProveedoresActivos}
                </div>
              </div>
            </div>
            <div
              style={{ width: '100%', height: '80px', cursor: 'pointer', display: 'flex', justifyContent: 'center', alignItems: 'center' }}
              onClick={() => {
                const chartData = {
                  labels: evolucionComprasActual.map(item => item.mes),
                  datasets: [{
                    label: 'Número de Proveedores Activos',
                    data: evolucionComprasActual.map(item => Number(item.proveedores.toFixed(0))),
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                  }]
                };
                const chartOptions = {
                  layout: getChartLayout(),
                  responsive: true,
                  maintainAspectRatio: false,
                  animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                  },
                  interaction: {
                    intersect: false,
                    mode: 'nearest'
                  },
                  plugins: {
                    tooltip: {
                      callbacks: {
                        label: (context: any) => `${context.parsed.y.toFixed(0)} proveedores activos`
                      }
                    },
                    datalabels: getSmartDataLabelsConfig((value: any) => value.toString())
                  },
                  // Core options
                  aspectRatio: 5 / 3,
                  elements: {
                    line: {
                      fill: false,
                      tension: 0.4
                    }
                  },
                  scales: {
                    x: {
                      display: true,
                      title: {
                        display: true,
                        text: 'Período'
                      }
                    },
                    y: {
                      display: true,
                      stacked: true,
                      title: {
                        display: true,
                        text: 'Número de Proveedores'
                      },
                      ticks: {
                        stepSize: 1
                      }
                    }
                  }
                };
                openChartDialog('Evolución del Número de Proveedores Activos', chartData, chartOptions, 'line');
              }}
            >
              <SemicircleGauge
                actualValue={indicadoresComprasActuales.numeroProveedoresActivos}
                maxValue={maxValueProveedores || 1} // Configuración global (todos los usuarios ven el mismo valor)
                label={(() => {
                  const proveedores = indicadoresComprasActuales.numeroProveedoresActivos;
                  const maxValue = maxValueProveedores || 1;
                  const step = maxValue / 8;

                  let score = 1;
                  if (proveedores >= maxValue) score = 8;
                  else if (proveedores >= maxValue - step) score = 7;
                  else if (proveedores >= maxValue - (step * 2)) score = 6;
                  else if (proveedores >= maxValue - (step * 3)) score = 5;
                  else if (proveedores >= maxValue - (step * 4)) score = 4;
                  else if (proveedores >= maxValue - (step * 5)) score = 3;
                  else if (proveedores >= maxValue - (step * 6)) score = 2;
                  else score = 1;

                  const labels = {
                    1: "Muy Bajo",
                    2: "Bajo",
                    3: "Regular",
                    4: "Bueno",
                    5: "Muy Bueno",
                    6: "Excelente",
                    7: "Sobresaliente",
                    8: "Excepcional"
                  };
                  return labels[score as keyof typeof labels];
                })()}
              />
            </div>
          </div>
        </div>

        {/* 2. Porcentaje de Reporte de Ganancia Operativa */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
          <div className="p-6">
            <div className="flex items-center mb-3">
              <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-green-500/10 flex items-center justify-center mr-3">
                <TrendingUp className="h-5 w-5 text-green-500" />
              </div>
              <div className="grow">
                <div className="flex items-center">
                  <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">PORCENTAJE DE REPORTE DE GANANCIA OPERATIVA</h3>
                  <CalculationButton
                    kpiId="reporteGanancia"
                    calculation="(Reportes de ganancia recibidos / Total de compras) * 100"
                  />
                </div>
                <div className="text-xl font-bold text-gray-900">
                  {indicadoresComprasActuales.porcentajeReporteGanancia}%
                </div>
              </div>
            </div>
            <div
              style={{ width: '100%', height: '80px', cursor: 'pointer' }}
              onClick={() => {
                const chartData = {
                  labels: evolucionComprasActual.map(item => item.mes),
                  datasets: [{
                    label: 'Porcentaje de Reporte de Ganancia (%)',
                    data: evolucionComprasActual.map(item => Number(item.reporteGanancia.toFixed(2))),
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    pointBackgroundColor: '#10b981',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                  }]
                };
                const chartOptions = {
                  layout: getChartLayout(),
                  animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                  },
                  interaction: {
                    intersect: false,
                    mode: 'nearest'
                  },
                  plugins: {
                    tooltip: {
                      callbacks: {
                        label: (context: any) => `${context.parsed.y.toFixed(2)}% de reportes`
                      }
                    },
                    datalabels: getSmartDataLabelsConfig((value: any) => `${Number(value).toFixed(2)}%`)
                  },
                  // Core options
                  aspectRatio: 5 / 3,
                  elements: {
                    line: {
                      fill: false,
                      tension: 0.4
                    }
                  },
                  scales: {
                    x: {
                      display: true,
                      title: {
                        display: true,
                        text: 'Período'
                      }
                    },
                    y: {
                      display: true,
                      stacked: true,
                      title: {
                        display: true,
                        text: 'Porcentaje (%)'
                      },
                      ticks: {
                        callback: (value: any) => `${Number(value).toFixed(2)}%`
                      }
                    }
                  }
                };
                openChartDialog('Evolución del Porcentaje de Reporte de Ganancia', chartData, chartOptions, 'line');
              }}
            >
              <Line
                data={{
                  labels: evolucionComprasActual.map(item => item.mes),
                  datasets: [{
                    label: 'Reporte Ganancia',
                    data: evolucionComprasActual.map(item => Number(item.reporteGanancia.toFixed(2))),
                    borderColor: '#10b981',
                    backgroundColor: '#10b981',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    pointBackgroundColor: '#10b981',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                  }]
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                  },
                  interaction: {
                    intersect: false,
                    mode: 'nearest'
                  },
                  plugins: {
                    legend: { display: false },
                    tooltip: {
                      animation: {
                        duration: 200
                      },
                      callbacks: {
                        label: (context) => `${context.parsed.y.toFixed(2)}%`
                      }
                    },
                    datalabels: {
                      display: false // Ocultar en gráficas pequeñas
                    }
                  },
                  scales: {
                    x: { display: false },
                    y: { display: false }
                  }
                }}
              />
            </div>
          </div>
        </div>

        {/* 3. Precios Promedios de Compra */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
          <div className="p-6">
            <div className="flex items-center mb-3">
              <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-yellow-500/10 flex items-center justify-center mr-3">
                <DollarSign className="h-5 w-5 text-yellow-500" />
              </div>
              <div className="grow">
                <div className="flex items-center">
                  <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">PRECIOS PROMEDIOS DE COMPRA</h3>
                  <CalculationButton
                    kpiId="precioPromedio"
                    calculation="Promedio ponderado de precios de compra por litro"
                  />
                </div>
                <div className="text-xl font-bold text-gray-900">
                  ${indicadoresComprasActuales.preciosPromedioCompra}
                </div>
              </div>
            </div>
            <div
              style={{ width: '100%', height: '80px', cursor: 'pointer' }}
              onClick={() => {
                const chartData = {
                  labels: evolucionComprasActual.map(item => item.mes),
                  datasets: [{
                    label: 'Precio Promedio de Compra ($)',
                    data: evolucionComprasActual.map(item => Number(item.precioPromedio.toFixed(2))),
                    borderColor: '#f59e0b',
                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    pointBackgroundColor: '#f59e0b',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                  }]
                };
                const chartOptions = {
                  layout: getChartLayout(),
                  animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                  },
                  interaction: {
                    intersect: false,
                    mode: 'nearest'
                  },
                  plugins: {
                    tooltip: {
                      callbacks: {
                        label: (context: any) => `$${Number(context.parsed.y).toFixed(2)} por litro`
                      }
                    },
                    datalabels: getSmartDataLabelsConfig((value: any) => `$${Number(value).toFixed(2)}`)
                  },
                  // Core options
                  aspectRatio: 5 / 3,
                  elements: {
                    line: {
                      fill: false,
                      tension: 0.4
                    }
                  },
                  scales: {
                    x: {
                      display: true,
                      title: {
                        display: true,
                        text: 'Período'
                      }
                    },
                    y: {
                      display: true,
                      stacked: true,
                      title: {
                        display: true,
                        text: 'Precio Promedio ($)'
                      },
                      ticks: {
                        callback: (value: any) => `$${Number(value).toFixed(2)}`
                      }
                    }
                  }
                };
                openChartDialog('Evolución de los Precios Promedios de Compra', chartData, chartOptions, 'line');
              }}
            >
              <Line
                data={{
                  labels: evolucionComprasActual.map(item => item.mes),
                  datasets: [{
                    label: 'Precio Promedio',
                    data: evolucionComprasActual.map(item => Number(item.precioPromedio.toFixed(2))),
                    borderColor: '#f59e0b',
                    backgroundColor: '#f59e0b',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    pointBackgroundColor: '#f59e0b',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                  }]
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                  },
                  interaction: {
                    intersect: false,
                    mode: 'nearest'
                  },
                  plugins: {
                    legend: { display: false },
                    tooltip: {
                      animation: {
                        duration: 200
                      },
                      callbacks: {
                        label: (context) => `$${Number(context.parsed.y).toFixed(2)}`
                      }
                    },
                    datalabels: {
                      display: false // Ocultar en gráficas pequeñas
                    }
                  },
                  scales: {
                    x: { display: false },
                    y: { display: false }
                  }
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Segunda Fila - 1 Columna Grande */}
      <div className="my-2">
        {/* 4. Diferencial entre Precio de Compra Real y Precio de Terminal PEMEX - GRANDE */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-4 relative">
          <div className="relative">
            <div className="after:w-20 after:h-20 after:absolute after:-top-8 after:-end-8 after:-z-10 after:bg-[url('../assets/images/pattern/dot5.svg')] hidden sm:block" />
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-purple-500/10 flex items-center justify-center mr-3">
                  <Activity className="h-5 w-5 text-purple-500" />
                </div>
                <div>
                  <div className="flex items-center">
                    <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">DIFERENCIAL ENTRE PRECIO DE COMPRA REAL Y PRECIO DE TERMINAL PEMEX</h3>
                    <CalculationButton
                      kpiId="diferencial"
                      calculation="Precio de compra real - Precio de terminal PEMEX"
                    />
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-xl font-bold text-gray-900">
                  {indicadoresComprasActuales.diferencialPrecioPemex}%
                </div>
                <p className="text-xs text-gray-400">Diferencial actual</p>
              </div>
            </div>
          </div>
          <div
            style={{ width: '100%', height: '150px', cursor: 'pointer' }}
            onClick={() => {
              const chartData = {
                labels: evolucionComprasActual.map(item => item.mes),
                datasets: [{
                  label: 'Diferencial de Precio (%)',
                  data: evolucionComprasActual.map(item => Number(item.diferencial.toFixed(2))),
                  borderColor: '#8b5cf6',
                  backgroundColor: 'rgba(139, 92, 246, 0.1)',
                  borderWidth: 3,
                  fill: true,
                  tension: 0.4,
                  pointRadius: 4,
                  pointHoverRadius: 6,
                  pointBackgroundColor: '#8b5cf6',
                  pointBorderColor: '#ffffff',
                  pointBorderWidth: 2
                }]
              };
              const chartOptions = {
                layout: getChartLayout(),
                animation: {
                  duration: 1000,
                  easing: 'easeInOutQuart'
                },
                interaction: {
                  intersect: false,
                  mode: 'nearest'
                },
                plugins: {
                  tooltip: {
                    callbacks: {
                      label: (context: any) => `${context.parsed.y.toFixed(2)}% de diferencial`
                    }
                  },
                  datalabels: getSmartDataLabelsConfig((value: any) => `${Number(value).toFixed(2)}%`)
                },
                // Core options
                aspectRatio: 5 / 3,
                elements: {
                  line: {
                    fill: false,
                    tension: 0.4
                  }
                },
                scales: {
                  x: {
                    display: true,
                    title: {
                      display: true,
                      text: 'Período'
                    }
                  },
                  y: {
                    display: true,
                    stacked: true,
                    title: {
                      display: true,
                      text: 'Diferencial de Precio (%)'
                    },
                    ticks: {
                      callback: (value: any) => `${Number(value).toFixed(2)}%`
                    }
                  }
                }
              };
              openChartDialog('Diferencial entre Precio de Compra Real y Precio de Terminal PEMEX', chartData, chartOptions, 'line');
            }}
          >
            <Line
              data={{
                labels: evolucionComprasActual.map(item => item.mes),
                datasets: [{
                  label: 'Diferencial',
                  data: evolucionComprasActual.map(item => Number(item.diferencial.toFixed(2))),
                  borderColor: '#8b5cf6',
                  backgroundColor: '#8b5cf6',
                  borderWidth: 3,
                  fill: false,
                  tension: 0.4,
                  pointRadius: 4,
                  pointHoverRadius: 6,
                  pointBackgroundColor: '#8b5cf6',
                  pointBorderColor: '#ffffff',
                  pointBorderWidth: 2
                }]
              }}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                  duration: 1000,
                  easing: 'easeInOutQuart'
                },
                interaction: {
                  intersect: false,
                  mode: 'nearest'
                },
                plugins: {
                  legend: { display: false },
                  tooltip: {
                    animation: {
                      duration: 200
                    },
                    callbacks: {
                      label: (context) => `${context.parsed.y.toFixed(2)}%`
                    }
                  },
                  datalabels: {
                    display: false // Ocultar en gráficas pequeñas
                  }
                },
                scales: {
                  x: { display: false },
                  y: { display: false }
                }
              }}
            />
          </div>
        </div>
      </div>

      {/* Tercera Fila - 1 Columna Grande para el gráfico de pastel */}
      <div className="my-2">
        {/* 5. % Compra por Proveedor - GRANDE */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-4 relative">
          <div className="relative">
            <div className="before:w-20 before:h-20 before:absolute before:-bottom-12 before:-start-12 before:-z-10 before:bg-[url('../assets/images/pattern/dot2.svg')] hidden sm:block" />
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-emerald-500/10 flex items-center justify-center mr-3">
                  <Package className="h-5 w-5 text-emerald-500" />
                </div>
                <div>
                  <div className="flex items-center">
                    <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">EVOLUCIÓN DE % COMPRA POR PROVEEDOR</h3>
                    <CalculationButton
                      kpiId="compraPorProveedor"
                      calculation="Evolución temporal del porcentaje de compras por proveedor"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            style={{ width: '100%', height: '300px', cursor: 'pointer' }}
            onClick={() => {
              // Crear datos de evolución por proveedor basados en los datos actuales
              const proveedoresData = [
                { nombre: 'Proveedor A', color: '#10b981' },
                { nombre: 'Proveedor B', color: '#f59e0b' },
                { nombre: 'Proveedor C', color: '#ef4444' },
                { nombre: 'Proveedor D', color: '#3b82f6' },
                { nombre: 'Otros', color: '#8b5cf6' }
              ];

              // Crear datasets para cada proveedor usando los datos de evolución
              const datasets = proveedoresData.map((proveedor) => {
                const data = evolucionComprasActual.map((item: any) => {
                  // Mapear los datos de porcentaje por proveedor desde los datos mock
                  switch(proveedor.nombre) {
                    case 'Proveedor A': return Number((item.porcentajeA || 0).toFixed(2));
                    case 'Proveedor B': return Number((item.porcentajeB || 0).toFixed(2));
                    case 'Proveedor C': return Number((item.porcentajeC || 0).toFixed(2));
                    case 'Proveedor D': return Number((item.porcentajeD || 0).toFixed(2));
                    case 'Otros': return Number((item.porcentajeOtros || 0).toFixed(2));
                    default: return 0;
                  }
                });

                return {
                  label: proveedor.nombre,
                  data: data,
                  borderColor: proveedor.color,
                  backgroundColor: `${proveedor.color}20`,
                  borderWidth: 3,
                  fill: false,
                  tension: 0.4,
                  pointRadius: 4,
                  pointHoverRadius: 6,
                  pointBackgroundColor: proveedor.color,
                  pointBorderColor: '#ffffff',
                  pointBorderWidth: 2
                };
              });

              const chartData = {
                labels: evolucionComprasActual.map((item: any) => item.mes),
                datasets: datasets
              };

              const chartOptions = {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                  duration: 1200,
                  easing: 'easeInOutQuart'
                },
                interaction: {
                  intersect: false,
                  mode: 'index'
                },
                plugins: {
                  legend: {
                    display: true,
                    position: 'bottom',
                    labels: {
                      usePointStyle: true,
                      padding: 20
                    }
                  },
                  tooltip: {
                    animation: {
                      duration: 200
                    },
                    callbacks: {
                      label: (context: any) => `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`
                    }
                  },
                  datalabels: getSmartDataLabelsConfig((value: any) => `${value}%`)
                },
                scales: {
                  x: {
                    display: true,
                    title: {
                      display: true,
                      text: 'Período'
                    }
                  },
                  y: {
                    display: true,
                    title: {
                      display: true,
                      text: 'Porcentaje (%)'
                    },
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                      callback: (value: any) => `${Number(value).toFixed(1)}%`
                    }
                  }
                }
              };
              openChartDialog('Evolución de % Compra por Proveedor', chartData, chartOptions, 'line');
            }}
          >
            <Line
              data={{
                labels: evolucionComprasActual.map((item: any) => item.mes),
                datasets: [
                  {
                    label: 'Proveedor A',
                    data: evolucionComprasActual.map((item: any) => Number((item.porcentajeA || 0).toFixed(2))),
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    pointBackgroundColor: '#10b981',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                  },
                  {
                    label: 'Proveedor B',
                    data: evolucionComprasActual.map((item: any) => Number((item.porcentajeB || 0).toFixed(2))),
                    borderColor: '#f59e0b',
                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    pointBackgroundColor: '#f59e0b',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                  },
                  {
                    label: 'Proveedor C',
                    data: evolucionComprasActual.map((item: any) => Number((item.porcentajeC || 0).toFixed(2))),
                    borderColor: '#ef4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    pointBackgroundColor: '#ef4444',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                  },
                  {
                    label: 'Proveedor D',
                    data: evolucionComprasActual.map((item: any) => Number((item.porcentajeD || 0).toFixed(2))),
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                  },
                  {
                    label: 'Otros',
                    data: evolucionComprasActual.map((item: any) => Number((item.porcentajeOtros || 0).toFixed(2))),
                    borderColor: '#8b5cf6',
                    backgroundColor: 'rgba(139, 92, 246, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    pointBackgroundColor: '#8b5cf6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                  }
                ]
              }}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                  duration: 1200,
                  easing: 'easeInOutQuart'
                },
                interaction: {
                  intersect: false,
                  mode: 'index'
                },
                plugins: {
                  legend: {
                    display: true,
                    position: 'bottom',
                    labels: {
                      usePointStyle: true,
                      padding: 15,
                      boxWidth: 6
                    }
                  },
                  tooltip: {
                    animation: {
                      duration: 200
                    },
                    callbacks: {
                      label: (context: any) => `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`
                    }
                  },
                  datalabels: {
                    display: false // Ocultar etiquetas en gráfica pequeña
                  }
                },
                scales: {
                  x: {
                    display: false
                  },
                  y: {
                    display: false
                  }
                }
              }}
            />
          </div>
        </div>
      </div>

          </>
        )}


    </div>
  );
};

export default AdminDashboardCompras;
