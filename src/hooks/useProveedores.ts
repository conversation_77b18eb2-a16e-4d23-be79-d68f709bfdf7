import { useState, useEffect } from 'react';
import { getProveedores, type ProveedorData } from '@/app/actions/proveedores';

// Cache global para proveedores
let proveedoresCache: ProveedorData[] | null = null;
let cacheTimestamp: number | null = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

export function useProveedores() {
  const [proveedores, setProveedores] = useState<ProveedorData[]>(proveedoresCache || []);
  const [loading, setLoading] = useState(!proveedoresCache);
  const [error, setError] = useState<string | null>(null);

  const loadProveedores = async (forceRefresh = false) => {
    // Verificar si el cache es válido
    const now = Date.now();
    const isCacheValid = proveedoresCache && 
                        cacheTimestamp && 
                        (now - cacheTimestamp) < CACHE_DURATION;

    if (isCacheValid && !forceRefresh) {
      setProveedores(proveedoresCache!);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const result = await getProveedores({ activo: true });
      
      if (result.success && result.data) {
        // Actualizar cache global
        proveedoresCache = result.data;
        cacheTimestamp = now;
        
        setProveedores(result.data);
      } else {
        setError(result.error || 'Error al cargar proveedores');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error al cargar proveedores:', err);
    } finally {
      setLoading(false);
    }
  };

  // Función para agregar un nuevo proveedor al cache
  const addProveedorToCache = (newProveedor: ProveedorData) => {
    if (proveedoresCache) {
      proveedoresCache = [...proveedoresCache, newProveedor];
      setProveedores(proveedoresCache);
    }
  };

  // Función para invalidar el cache
  const invalidateCache = () => {
    proveedoresCache = null;
    cacheTimestamp = null;
  };

  // Cargar proveedores al montar el hook
  useEffect(() => {
    loadProveedores();
  }, []);

  return {
    proveedores,
    loading,
    error,
    loadProveedores,
    addProveedorToCache,
    invalidateCache,
    // Funciones de utilidad
    getDefaultProveedores: () => {
      if (proveedores.length >= 3) {
        return [
          { nombre: proveedores[0].nombre, porcentaje: 0 },
          { nombre: proveedores[1].nombre, porcentaje: 0 },
          { nombre: proveedores[2].nombre, porcentaje: 0 }
        ];
      }
      return [
        { nombre: "", porcentaje: 0 },
        { nombre: "", porcentaje: 0 },
        { nombre: "", porcentaje: 0 }
      ];
    },
    getAvailableProveedores: (usedNames: string[]) => {
      return proveedores.filter(p => !usedNames.includes(p.nombre));
    }
  };
}
